package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class AlbumPlayListControl extends BasePlayListControl {
    private static final int LOAD_DATA = 0;
    private static final int LOAD_DATA_NEXT = 1;
    private static final int LOAD_DATA_PRE = 2;
    public static final int LOAD_DATA_PAGE = 3; //获取整页数据
    private static final int LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM = 4; //获取整页数据后播放下一个
    private static final int LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM = 5; //获取整页数据后播放上一个
    private AlbumRequest mAlbumRequest;
    private AudioRequest mAudioRequest;
    private PlayerBuilder tempPlayerBuilder;
    private int mPageSize = PlayerConstants.PAGE_NUMBER_10;
    private AlbumDetails mAlbumDetails;

    public AlbumPlayListControl() {
        super();
        mAlbumRequest = new AlbumRequest();
        mAudioRequest = new AudioRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "album");
        if (playerBuilder.getType() == PlayerConstants.RESOURCES_TYPE_AUDIO) {
            PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "play audio, get audio detail");
            tempPlayerBuilder = new CustomPlayerBuilder();
            ((CustomPlayerBuilder) tempPlayerBuilder).setChildId(playerBuilder.getId()).setType(playerBuilder.getType());
            getAudioInfo(iPlayListGetListener);
            return;
        }
        tempPlayerBuilder = playerBuilder;
        super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
        getAlbumInfo(iPlayListGetListener, false);
    }

    @Override
    public int getCurPosition() {
        if (mPosition != -1 && !isInList(mPlayItemArrayList, mCurPlayItem)) {
            return -1;
        }
        return mPosition;
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getNextPlayItem(iPlayListGetListener);
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getPrePlayItem(iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "album get next page, current position=" + mPosition + ", current list size=" + mPlayItemArrayList.size());
        long albumId = string2Long(mPlaylistInfo.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "requesting next page for albumId=" + albumId + ", nextPage=" + mPlaylistInfo.getNextPage());

        AlbumPlayItem invalidPlayItem = new AlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        invalidPlayItem.setInfodata(infoData);

        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getNextPage(), new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage.success", "received " + (listBasePageResult.getDataList() != null ? listBasePageResult.getDataList().size() : 0) + " items from server");
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage.success", "converted playItemArrayList is empty, notifying error");
                    notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_NULL, -1);
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage.success", "converted " + playItemArrayList.size() + " playItems, updating playlist");
                updatePlayListInfo(LOAD_DATA_NEXT, listBasePageResult);
                updatePlayListContent(LOAD_DATA_NEXT, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(invalidPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "album get pre page playlist...");
        if (!mPlaylistInfo.isHasPrePage()) {
            return;
        }
        long albumId = string2Long(mPlaylistInfo.getId());

        AlbumPlayItem albumPlayItem = new AlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        albumPlayItem.setInfodata(infoData);

        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getPrePage(), new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_NULL, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_PRE, listBasePageResult);
                updatePlayListContent(LOAD_DATA_PRE, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
                notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_PAGE_SERVER, e.getCode());
            }
        });
    }

    @Override
    public void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "album get page data playlist...");

        int errorCode1 = -1;
        int errorCode2 = -1;
        if (type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_NEXT_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PRE_ITEM_PAGE_SERVER;
        } else if (type == LOAD_DATA_PAGE) {
            errorCode1 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_NULL;
            errorCode2 = PlayerConstants.ERROR_CODE_PLAY_LIST_ONE_KEY_TO_TOP_ALBUM_PAGE_SERVER;
        }
        int finalErrorCode1 = errorCode1;
        int finalErrorCode2 = errorCode2;
        long albumId = string2Long(mPlaylistInfo.getId());

        AlbumPlayItem albumPlayItem = new AlbumPlayItem();
        InfoData infoData = new InfoData();
        infoData.setAlbumId(albumId);
        albumPlayItem.setInfodata(infoData);
        albumPlayItem.setAudioId(audioId);

        loadPlayListData(albumId, audioId, mPlaylistInfo.getSort(), pageNum, new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "");
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, finalErrorCode1, -1);
                    return;
                }
                updatePlayListInfo(type, listBasePageResult);
                updatePlayListContent(type, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, albumPlayItem, finalErrorCode2, e.getCode());
                notifyPlayListChangeError(albumPlayItem, finalErrorCode2, e.getCode());
            }
        });
    }

    private void loadPlayListData(long albumId, int sort, int pageNum, IDataListCallback<BasePageResult<List<AudioDetails>>> iDataListCallback) {
        loadPlayListData(albumId, 0, sort, pageNum, iDataListCallback);
    }

    /**
     * 加载数据
     *
     * @param albumId
     * @param audioId
     * @param sort
     * @param pageNum
     * @param iDataListCallback
     */
    private void loadPlayListData(long albumId, long audioId, int sort, int pageNum, IDataListCallback<BasePageResult<List<AudioDetails>>> iDataListCallback) {
        mAlbumRequest.getPlaylist(albumId, audioId, sort, mPageSize, pageNum, new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> listBasePageResult) {
//                if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
//                    if (iDataListCallback != null) {
//                        iDataListCallback.error();
//                    }
//                    return;
//                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(listBasePageResult);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e);
                }
            }
        });
    }

    private int getNotifyPlayListIndex(ArrayList<PlayItem> playItemArrayList, long audioId) {
        if (audioId <= 0) {
            return 0;
        }
        for (int i = 0; i < playItemArrayList.size(); i++) {
            PlayItem playItem = playItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }

            if (playItem.getAudioId() == audioId) {
                return i;
            }
        }

        return 0;
    }

    private void getAlbumInfo(final IPlayListGetListener iPlayListGetListener, boolean isFromAudio) {
        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo");
        long albumId = string2Long(tempPlayerBuilder.getId());
        long audioId = 0;
        if (tempPlayerBuilder instanceof CustomPlayerBuilder) {
            audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        }

        long finalAudioId = audioId;
        AlbumPlayItem albumPlayItem = new AlbumPlayItem();
        albumPlayItem.setFromAudio(isFromAudio);
        albumPlayItem.getInfoData().setAlbumId(albumId);
        albumPlayItem.setAudioId(audioId);

        mAlbumRequest.getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails albumDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success");
                mAlbumDetails = albumDetails;
                initPlayListInfo(albumDetails);

                if (PlayerPreconditions.checkNull(albumDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, but data is null");
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_NULL, -1);
                    return;
                }
                albumPlayItem.setNoSubscribe(albumDetails.getNoSubscribe());
                albumPlayItem.setListenCount(albumDetails.getCountNum());
                albumPlayItem.getInfoData().setAlbumName(albumDetails.getName());
                albumPlayItem.getInfoData().setAlbumPic(albumDetails.getImg());
                albumPlayItem.getAlbumInfoData().setASentenceRecommend(albumDetails.getASentenceRecommend());
                mPlaylistInfo.setNoSubscribe(albumDetails.getNoSubscribe());
                mPlaylistInfo.setEnableReverse(albumDetails.getEnableReverse() == AlbumDetails.REVERSE_ENABLE);
                tempPlayerBuilder.setNoSubscribe(albumDetails.getNoSubscribe());
                if (albumDetails.getIsOnline() != PlayerConstants.ALBUM_ONLINE) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, offline");
                    notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_ALBUM_OFFLINE, -1);
                    return;
                }

                loadPlayListData(albumId, finalAudioId, mPlaylistInfo.getSort(), 1, new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
                    @Override
                    public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                        if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success, but list is null");
                            notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success");
//                        AlbumPlayListControl.super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
                        updatePlayListInfo(LOAD_DATA, listBasePageResult);
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo, mAlbumDetails != null ? mAlbumDetails.getASentenceRecommend() : "", isFromAudio);
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_NULL, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(LOAD_DATA, finalAudioId, playItemArrayList, iPlayListGetListener);
                    }

                    @Override
                    public void error(ApiException e) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list error");
                        notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                        notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_PAGE_SERVER, e.getCode());
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get album info error");
                initPlayListInfo(null);
                notifyPlayListGetError(iPlayListGetListener, albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError(albumPlayItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });

    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void getAudioInfo(final IPlayListGetListener iPlayListGetListener) {
        long audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        mAudioRequest.getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                //此处获取到的audioDetails中只有id，不再包含url信息
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "success");
                mPlaylistInfo.setTempId(String.valueOf(audioDetails.getAlbumId()));
                mPlaylistInfo.setTempChildId(String.valueOf(audioDetails.getAudioId()));
                tempPlayerBuilder.setId(String.valueOf(audioDetails.getAlbumId()));
                getAlbumInfo(iPlayListGetListener, true);
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "error");
                AlbumPlayItem playItem = new AlbumPlayItem();
                playItem.setAudioId(audioId);
                // do PlayerManager#startNewBuilder#onDataGetError
                notifyPlayListGetError(iPlayListGetListener, playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
                notifyPlayListChangeError(playItem, PlayerConstants.ERROR_CODE_PLAY_LIST_INIT_ID_SERVER, e.getCode());
            }
        });
    }

    private void updatePlayListContent(int type, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        updatePlayListContent(type, 0, playItems, iPlayListGetListener);
    }

    private void updatePlayListContent(int type, long audioId, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        int needPlayIndex = 0;
        switch (type) {
            case LOAD_DATA:
                needPlayIndex = getNotifyPlayListIndex(playItems, audioId);
                break;
            case LOAD_DATA_NEXT:

                break;
            case LOAD_DATA_PRE:
                needPlayIndex = playItems.size() - 1;
                break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (++needPlayIndex >= playItems.size()) {
                    loadNextPage(iPlayListGetListener);
                    return;
                }
                break;
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (--needPlayIndex < 0) {
                    loadPrePage(iPlayListGetListener);
                    return;
                }
                break;
            default:
                break;
        }
        if (type == LOAD_DATA_PRE) {
            PlayItem playItem = getCurPlayItem();
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.addAll(0, playItems);
            setCurPosition(playItem);
        } else if (type == LOAD_DATA_PAGE || type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM || type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            PlayItem playItem = getPlayItem(mCurPlayItem, playItems);
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.clear();
            mPlayItemArrayList.addAll(playItems);
            setCurPosition(playItem);
        } else {
            List<PlayItem> diffItems = getDiffList(mPlayItemArrayList, playItems);
            mPlayItemArrayList.addAll(diffItems);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "needPlayIndex = " + needPlayIndex);

        // 修复LOAD_DATA_NEXT类型的播放项目选择问题
        if (type == LOAD_DATA_NEXT) {
            // 对于LOAD_DATA_NEXT，应该播放新追加数据的第一个项目
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: processing next page data");
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: original list size = " + (mPlayItemArrayList.size() - getDiffList(mPlayItemArrayList, playItems).size()));
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: new data size = " + playItems.size());

            List<PlayItem> diffItems = getDiffList(mPlayItemArrayList, playItems);
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: actual new items count = " + diffItems.size());

            if (!diffItems.isEmpty()) {
                // 智能选择下一个播放项：优先选择期数连续的项目
                PlayItem nextPlayItem = findNextSequentialItem(diffItems);
                PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: playing next sequential item, audioId = " + nextPlayItem.getAudioId());
                PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: merged list size = " + mPlayItemArrayList.size());
                notifyPlayListGet(iPlayListGetListener, nextPlayItem, mPlayItemArrayList);
            } else {
                // 服务器返回重复数据，尝试从原始数据中找到下一个期数的项目
                PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: no new items after deduplication, trying to find next sequential item from original data");
                PlayItem nextPlayItem = findNextSequentialItemFromOriginalData(playItems);
                if (nextPlayItem != null) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: found next sequential item from original data, audioId = " + nextPlayItem.getAudioId());
                    notifyPlayListGet(iPlayListGetListener, nextPlayItem, mPlayItemArrayList);
                } else {
                    PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "LOAD_DATA_NEXT: reached end of playlist");
                    notifyPlayListGetError(iPlayListGetListener, new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE, -1);
                }
            }
        } else {
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "Non-LOAD_DATA_NEXT: playing item at index " + needPlayIndex);
            notifyPlayListGet(iPlayListGetListener, playItems.get(needPlayIndex), playItems);
        }
        notifyPlayListChange(playItems);
    }

    private PlayItem getPlayItem(PlayItem playItem, List<PlayItem> playItemList) {
        if (playItem == null) {
            return null;
        }
        PlayItem item = null;
        for (int i = 0; i < playItemList.size(); i++) {
            if (playItem.getAudioId() == playItemList.get(i).getAudioId()) {
                item = playItemList.get(i);
                break;
            }
        }

        return item;
    }

    /**
     * 去重
     * 资讯内容更新比较即时，当拉取下一页的时候可能更新的多条内容，导致拉取重复数据，播放列表显示异常
     */
    public static List<PlayItem> getDiffList(List<PlayItem> fromList,
                                             List<PlayItem> onList) {
        List<PlayItem> result = new ArrayList<>();
        for (PlayItem bean : onList) {
            boolean hasValue = false;
            for (PlayItem item : fromList) {
                if (bean.getAudioId() == item.getAudioId()) {
                    hasValue = true;
                    break;
                }
            }
            if (!hasValue) {
                result.add(bean);
            }
        }
        return result;
    }

    /**
     * 从去重后的新数据中智能选择下一个播放项
     * 优先选择期数连续的项目
     */
    private PlayItem findNextSequentialItem(List<PlayItem> diffItems) {
        if (diffItems.isEmpty()) {
            return null;
        }

        // 获取当前播放项的期数
        PlayItem currentItem = getCurPlayItem();
        if (currentItem == null || currentItem instanceof InvalidPlayItem) {
            return diffItems.get(0);
        }

        int currentOrderNum = -1;
        if (currentItem instanceof AlbumPlayItem) {
            currentOrderNum = ((AlbumPlayItem) currentItem).getInfoData().getOrderNum();
        }

        if (currentOrderNum == -1) {
            // 如果无法获取期数信息，返回第一个项目
            return diffItems.get(0);
        }

        // 寻找期数为 currentOrderNum + 1 的项目
        for (PlayItem item : diffItems) {
            if (item instanceof AlbumPlayItem) {
                int orderNum = ((AlbumPlayItem) item).getInfoData().getOrderNum();
                if (orderNum == currentOrderNum + 1) {
                    return item;
                }
            }
        }

        // 如果没找到连续期数，返回第一个项目
        return diffItems.get(0);
    }

    /**
     * 从原始数据中寻找下一个期数的项目（用于处理去重后为空的情况）
     */
    private PlayItem findNextSequentialItemFromOriginalData(ArrayList<PlayItem> playItems) {
        if (playItems.isEmpty()) {
            return null;
        }

        // 获取当前播放项的期数
        PlayItem currentItem = getCurPlayItem();
        if (currentItem == null || currentItem instanceof InvalidPlayItem) {
            return null;
        }

        int currentOrderNum = -1;
        if (currentItem instanceof AlbumPlayItem) {
            currentOrderNum = ((AlbumPlayItem) currentItem).getInfoData().getOrderNum();
        }

        if (currentOrderNum == -1) {
            return null;
        }

        // 在原始数据中寻找期数为 currentOrderNum + 1 的项目
        for (PlayItem item : playItems) {
            if (item instanceof AlbumPlayItem) {
                int orderNum = ((AlbumPlayItem) item).getInfoData().getOrderNum();
                if (orderNum == currentOrderNum + 1) {
                    // 检查这个项目是否已经在当前播放列表中
                    if (!isInList(mPlayItemArrayList, item)) {
                        // 如果不在列表中，添加到列表并返回
                        mPlayItemArrayList.add(item);
                        return item;
                    } else {
                        // 如果已经在列表中，直接返回（用于播放）
                        return item;
                    }
                }
            }
        }

        return null;
    }

    public static boolean isInList(List<PlayItem> fromList,
                                   PlayItem playItem) {
        boolean hasValue = false;
        if (playItem == null) {
            return false;
        }
        for (PlayItem item : fromList) {
            if (playItem.getAudioId() == item.getAudioId()) {
                hasValue = true;
                break;
            }
        }

        return hasValue;
    }

    private void initPlayListInfo(AlbumDetails albumDetails) {
        if (!StringUtil.isEmpty(mPlaylistInfo.getTempId())) {
            mPlaylistInfo.setId(mPlaylistInfo.getTempId());
        }
        mPlaylistInfo.setChildId(null);
        if (albumDetails != null) {
            mPlaylistInfo.setSort(tempPlayerBuilder.getSort());
            mPlaylistInfo.setAlbumName(albumDetails.getName());
            mPlaylistInfo.setAlbumPic(albumDetails.getImg());
            mPlaylistInfo.setCountNum(albumDetails.getCountNum());
            mPlaylistInfo.setFollowedNum(albumDetails.getFollowedNum());
            mPlaylistInfo.setListenNum(albumDetails.getListenNum());
            mPlaylistInfo.setSourceName(albumDetails.getSourceName());
            mPlaylistInfo.setSourceLogo(albumDetails.getSourceLogo());
            mPlaylistInfo.setBreakPointContinue(albumDetails.getBreakPointContinue());
        }
    }

    private void updatePlayListInfo(int type, BasePageResult basePageResult) {
        switch (type) {
            case LOAD_DATA_PRE: {
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_NEXT: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA:
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
            case LOAD_DATA_PAGE:
                mPlaylistInfo.resetNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.resetPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
                break;
            default:
                break;
        }
        mPlaylistInfo.setPageIndex(String.valueOf(basePageResult.getNextPage()));
        mPlaylistInfo.setAllSize(basePageResult.getCount());
    }

    private void setPageSize(int pageSize) {
        mPageSize = pageSize;
    }
}
